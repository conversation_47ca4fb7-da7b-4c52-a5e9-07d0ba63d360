package net.summerfarm.mall.service.impl;

import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.model.domain.Trolley;
import net.summerfarm.mall.model.dto.order.UserContextParam;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.model.vo.price.TakeActualPriceVO;
import net.summerfarm.mall.service.OrderCalcService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * OrderCalcService 使用示例
 * 展示如何使用新的用户上下文参数方法来计算任意用户的任意SKU的到手价
 * 
 * <AUTHOR>
 */
@Component
public class OrderCalcServiceUsageExample {

    @Resource
    private OrderCalcService orderCalcService;

    /**
     * 示例1：计算单店用户的SKU到手价
     */
    public void example1_SingleStoreUserTakePrice() {
        System.out.println("=== 示例1：单店用户到手价计算 ===");
        
        // 1. 创建用户上下文
        UserContextParam userContext = new UserContextParam();
        userContext.setMId(12345L);
        userContext.setAccountId(67890L);
        userContext.setMname("张三的小店");
        userContext.setMajorMerchant(false); // 单店
        userContext.setDirect(2); // 现结
        userContext.setSkuShow(2); // 全量
        userContext.setServer(1); // 服务区内
        userContext.setBusinessLine(0); // 鲜沐
        userContext.setSize("单店");
        userContext.setOpenId("wx_openid_123");
        
        // 设置区域信息
        Area area = new Area();
        area.setAreaNo(1001);
        area.setAreaName("杭州市余杭区");
        userContext.setArea(area);

        // 2. 创建订单信息
        PlaceOrderVO orderVO = new PlaceOrderVO();
        orderVO.setTakePriceFlag(true); // 获取明细，不均摊优惠
        orderVO.setIsTakePrice(0); // 到手价计算
        
        // 添加要计算的SKU
        List<Trolley> orderNow = new ArrayList<>();
        Trolley trolley = new Trolley();
        trolley.setSku("************"); // 要计算的SKU
        trolley.setQuantity(2); // 数量
        trolley.setProductType(0); // 普通商品
        trolley.setSuitId(0);
        orderNow.add(trolley);
        orderVO.setOrderNow(orderNow);

        // 3. 调用计算方法
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, userContext);

        // 4. 处理结果
        if (result != null && !result.isEmpty()) {
            for (TakeActualPriceVO priceVO : result) {
                System.out.println("SKU: " + priceVO.getSku());
                System.out.println("数量: " + priceVO.getQuantity());
                System.out.println("原价: " + priceVO.getPrice());
                System.out.println("到手价: " + priceVO.getTakeActualPrice());
                System.out.println("---");
            }
        } else {
            System.out.println("未获取到到手价信息");
        }
    }

    /**
     * 示例2：计算大客户的SKU到手价（应该返回空）
     */
    public void example2_MajorMerchantTakePrice() {
        System.out.println("=== 示例2：大客户到手价计算 ===");
        
        // 1. 创建大客户上下文
        UserContextParam userContext = new UserContextParam();
        userContext.setMId(54321L);
        userContext.setAccountId(98765L);
        userContext.setMname("李四大客户");
        userContext.setMajorMerchant(true); // 大客户
        userContext.setAdminId(1001);
        userContext.setDirect(1); // 账期
        userContext.setSkuShow(1); // 定量
        userContext.setServer(1); // 服务区内
        userContext.setBusinessLine(0); // 鲜沐
        userContext.setSize("大客户");
        
        Area area = new Area();
        area.setAreaNo(1001);
        userContext.setArea(area);

        // 2. 创建订单信息
        PlaceOrderVO orderVO = new PlaceOrderVO();
        orderVO.setTakePriceFlag(true);
        orderVO.setIsTakePrice(0);
        
        List<Trolley> orderNow = new ArrayList<>();
        Trolley trolley = new Trolley();
        trolley.setSku("************");
        trolley.setQuantity(5);
        trolley.setProductType(0);
        trolley.setSuitId(0);
        orderNow.add(trolley);
        orderVO.setOrderNow(orderNow);

        // 3. 调用计算方法
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, userContext);

        // 4. 处理结果
        if (result != null && result.isEmpty()) {
            System.out.println("大客户不返回到手价信息（符合预期）");
        } else {
            System.out.println("意外：大客户返回了到手价信息");
        }
    }

    /**
     * 示例3：批量计算多个SKU的到手价
     */
    public void example3_BatchSkuTakePrice() {
        System.out.println("=== 示例3：批量SKU到手价计算 ===");
        
        // 1. 创建用户上下文
        UserContextParam userContext = UserContextParam.createDefaultSingleStore();
        userContext.setMId(11111L);
        userContext.setAccountId(22222L);
        userContext.setMname("王五批发店");

        // 2. 创建包含多个SKU的订单
        PlaceOrderVO orderVO = new PlaceOrderVO();
        orderVO.setTakePriceFlag(true);
        orderVO.setIsTakePrice(0);
        
        List<Trolley> orderNow = new ArrayList<>();
        
        // 添加多个不同的SKU
        String[] skus = {"************", "************", "***********"};
        Integer[] quantities = {1, 2, 3};
        
        for (int i = 0; i < skus.length; i++) {
            Trolley trolley = new Trolley();
            trolley.setSku(skus[i]);
            trolley.setQuantity(quantities[i]);
            trolley.setProductType(0);
            trolley.setSuitId(0);
            orderNow.add(trolley);
        }
        orderVO.setOrderNow(orderNow);

        // 3. 调用计算方法
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, userContext);

        // 4. 处理结果
        System.out.println("批量计算结果：");
        if (result != null) {
            for (TakeActualPriceVO priceVO : result) {
                System.out.println("SKU: " + priceVO.getSku() + 
                                 ", 数量: " + priceVO.getQuantity() + 
                                 ", 到手价: " + priceVO.getTakeActualPrice());
            }
        }
    }

    /**
     * 示例4：POP商城用户到手价计算
     */
    public void example4_PopMerchantTakePrice() {
        System.out.println("=== 示例4：POP商城用户到手价计算 ===");
        
        // 1. 创建POP用户上下文
        UserContextParam userContext = UserContextParam.createDefaultPopMerchant();
        userContext.setMId(33333L);
        userContext.setAccountId(44444L);
        userContext.setMname("赵六POP店铺");

        // 2. 创建订单信息
        PlaceOrderVO orderVO = new PlaceOrderVO();
        orderVO.setTakePriceFlag(true);
        orderVO.setIsTakePrice(0);
        
        List<Trolley> orderNow = new ArrayList<>();
        Trolley trolley = new Trolley();
        trolley.setSku("************");
        trolley.setQuantity(1);
        trolley.setProductType(0);
        trolley.setSuitId(0);
        orderNow.add(trolley);
        orderVO.setOrderNow(orderNow);

        // 3. 调用计算方法
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, userContext);

        // 4. 处理结果
        System.out.println("POP商城用户计算结果：");
        if (result != null && !result.isEmpty()) {
            TakeActualPriceVO priceVO = result.get(0);
            System.out.println("SKU: " + priceVO.getSku());
            System.out.println("到手价: " + priceVO.getTakeActualPrice());
            System.out.println("业务线: POP商城");
        }
    }

    /**
     * 示例5：省心送到手价计算
     */
    public void example5_TimingOrderTakePrice() {
        System.out.println("=== 示例5：省心送到手价计算 ===");
        
        // 1. 创建用户上下文
        UserContextParam userContext = UserContextParam.createDefaultSingleStore();
        userContext.setMId(55555L);
        userContext.setAccountId(66666L);

        // 2. 创建省心送订单
        PlaceOrderVO orderVO = new PlaceOrderVO();
        orderVO.setTakePriceFlag(true);
        orderVO.setIsTakePrice(0);
        orderVO.setTimingSkuPrice(1); // 省心送标识
        orderVO.setTimingRuleId(1001); // 省心送规则ID
        
        List<Trolley> orderNow = new ArrayList<>();
        Trolley trolley = new Trolley();
        trolley.setSku("************");
        trolley.setQuantity(1);
        trolley.setProductType(0);
        trolley.setSuitId(0);
        orderNow.add(trolley);
        orderVO.setOrderNow(orderNow);

        // 3. 调用计算方法
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, userContext);

        // 4. 处理结果
        System.out.println("省心送计算结果：");
        if (result != null && !result.isEmpty()) {
            for (TakeActualPriceVO priceVO : result) {
                System.out.println("省心送SKU: " + priceVO.getSku());
                System.out.println("省心送到手价: " + priceVO.getTakeActualPrice());
            }
        }
    }

    /**
     * 运行所有示例
     */
    public void runAllExamples() {
        example1_SingleStoreUserTakePrice();
        System.out.println();
        
        example2_MajorMerchantTakePrice();
        System.out.println();
        
        example3_BatchSkuTakePrice();
        System.out.println();
        
        example4_PopMerchantTakePrice();
        System.out.println();
        
        example5_TimingOrderTakePrice();
        System.out.println();
        
        System.out.println("所有示例运行完成！");
    }
}
