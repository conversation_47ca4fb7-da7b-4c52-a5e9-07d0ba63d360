package net.summerfarm.mall;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.BaseMvcTest;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.PaymentManageListListener;
import net.summerfarm.mall.mapper.AreaMapper;
import net.summerfarm.mall.mapper.ContactMapper;
import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.model.domain.Contact;
import net.summerfarm.mall.service.DeliveryService;
import net.summerfarm.mall.service.InventoryService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/8/25  12:42
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class EsTest extends BaseMvcTest {
    @Resource
    private InventoryService inventoryService;

    @Test
    public void testEs(){

    }

    @Override
    public Long loginMId() {
        return 344066L;
    }

    @Resource
    PaymentManageListListener paymentManageListListener;
    @Test
    public void tttt(){
        String ss = "{\"data\":\"{\\\"masterOrder\\\":{\\\"accountId\\\":3384,\\\"areaNo\\\":1001,\\\"createTime\\\":\\\"2023-10-31 19:15:44\\\",\\\"id\\\":753,\\\"mId\\\":344066,\\\"mSize\\\":\\\"单店\\\",\\\"masterOrderNo\\\":\\\"M01169875094465934\\\",\\\"orderTime\\\":\\\"2023-10-31 19:15:45\\\",\\\"originPrice\\\":114.00,\\\"status\\\":1,\\\"totalPrice\\\":86.00,\\\"type\\\":0},\\\"masterPayment\\\":{\\\"endTime\\\":\\\"2023-10-31 19:15:45\\\",\\\"id\\\":266,\\\"masterOrderNo\\\":\\\"M01169875094465934\\\",\\\"money\\\":86.00,\\\"onlinePayEndTime\\\":\\\"2023-10-31 19:15:45\\\",\\\"payType\\\":\\\"鲜沐卡\\\",\\\"status\\\":1}}\",\"type\":\"MASTER_PAYMENT_NOTIFY_SUCCESS\"}";
        MQData delayData = JSONObject.parseObject(ss, MQData.class);
        paymentManageListListener.process(delayData);
    }


    @Resource
    private DeliveryService deliveryService;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private AreaMapper areaMapper;
}
