package net.summerfarm.mall.wechat.utils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.wechat.model.Signable;


@Slf4j
public class TenpayUtil {

    /**
     * 把对象转换成字符串
     * @param obj
     * @return String 转换成字符串,若对象为null,则返回空字符串.
     */
    public static String toString(Object obj) {
        if(obj == null)
            return "";
        return obj.toString();
    }

    /**
     * 把金额字符串转换为元为单位.
     *
     * @param total_fee 包含微信返回的金额字符串.
     * @return String 转换后的金额。
     */
    public static BigDecimal toYuan(String total_fee) {
        BigDecimal b1 = new BigDecimal(total_fee);
        BigDecimal b2 = new BigDecimal(100.00);

        BigDecimal div = b1.divide(b2, 2, BigDecimal.ROUND_HALF_UP); // 除
        return div;
    }
    /**
     * 把金额字符串转换为分为单位.
     *
     * @param total_fee 包含微信返回的金额字符串.
     *
     * @return String 转换后的金额。
     */
    public static String toFen(String total_fee) {

        BigDecimal b1 = new BigDecimal(total_fee);
        BigDecimal b2 = new BigDecimal(100.00);

        BigDecimal sum = b1.multiply(b2); // 相乘
        String total= sum.toString().substring(0, sum.toString().indexOf("."));// 去掉点号
        return total;
    }

    /**
     * 将时间字符串转换为 yyyy-MM-dd HH:mm:ss
     * @param date
     * @return String
     * @throws ParseException
     */

    public static String formatTime(String date)  {
        SimpleDateFormat formatter1=new SimpleDateFormat(DateUtils.LONG_DATE_FORMAT);
        SimpleDateFormat formatter2=new SimpleDateFormat(DateUtils.NUMBER_DATE_FORMAT);
        try {
            date=formatter1.format(formatter2.parse(date));
        } catch (ParseException e) {
           log.info("日期转换失败", e);
        }
        return date;
    }

    /**
     * 获取编码字符集
     * @param request
     * @param response
     * @return String
     */
    public static String getCharacterEncoding(HttpServletRequest request,
                                              HttpServletResponse response) {

        if(null == request || null == response) {
            return "UTF-8";
        }

        String enc = request.getCharacterEncoding();
        if(null == enc || "".equals(enc)) {
            enc = response.getCharacterEncoding();
        }

        if(null == enc || "".equals(enc)) {
            return "UTF-8";
        }

        return enc;
    }

    /**
     * 创建md5签名。
     */
    public static String getSign(Map<String,String> map,String mchKey){
        ArrayList<String> list = new ArrayList<String>();
        for(Map.Entry<String,String> entry:map.entrySet()){
            if (null != entry.getValue() && !"".equals(entry.getValue()) && !"sign".equals(entry.getKey())
                    && !"key".equals(entry.getKey()))
            {
                list.add(entry.getKey() + "=" + entry.getValue() + "&");
            }
        }
        int size = list.size();
        String [] arrayToSort = list.toArray(new String[size]);
        Arrays.sort(arrayToSort, String.CASE_INSENSITIVE_ORDER);
        StringBuilder sb = new StringBuilder();
        for(int i = 0; i < size; i ++) {
            sb.append(arrayToSort[i]);
        }
        String result = sb.toString();
        result += "key=" + mchKey;
        //Util.log("Sign Before MD5:" + result);
        result = MD5Util.MD5Encode(result, "UTF-8").toUpperCase();

        //Util.log("Sign Result:" + result);
        return result;
    }


    public static <T extends Signable> String getSign(T t, String mchKey) {
        String signStr = t.toSignString()+"&key="+mchKey;
        return MD5Util.MD5Encode(signStr, "UTF-8")
                .toUpperCase();
    }

}