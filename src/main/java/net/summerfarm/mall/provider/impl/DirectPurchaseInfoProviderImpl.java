package net.summerfarm.mall.provider.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.provider.DirectPurchaseInfoProvider;
import net.summerfarm.mall.client.req.DeliveryDateQueryReq;
import net.summerfarm.mall.client.req.DirectPurchaseInfoQueryReq;
import net.summerfarm.mall.client.resp.DirectPurchaseInfoQueryResp;
import net.summerfarm.mall.client.resp.OrderCompareCommonQueryResp;
import net.summerfarm.mall.enums.SampleApplyStatus;
import net.summerfarm.mall.mapper.DirectPurchaseInfoMapper;
import net.summerfarm.mall.mapper.OrderItemMapper;
import net.summerfarm.mall.model.domain.DirectPurchaseInfo;
import net.summerfarm.mall.model.domain.OrderItem;
import net.summerfarm.mall.model.domain.SampleApply;
import net.summerfarm.mall.model.domain.SampleSku;
import net.summerfarm.mall.model.vo.DeliveryPlanVO;
import net.summerfarm.mall.provider.converter.deliveryPlan.DeliveryPlan2Resp;
import net.summerfarm.mall.provider.converter.directPurchaseInfo.DirectPurchaseInfo2Resp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
@Component
public class DirectPurchaseInfoProviderImpl implements DirectPurchaseInfoProvider {

    @Resource
    DirectPurchaseInfoMapper directPurchaseInfoMapper;

    @Resource
    private OrderItemMapper orderItemMapper;


    @Override
    public DubboResponse<DirectPurchaseInfoQueryResp> getDirectPurchaseInfo(DirectPurchaseInfoQueryReq directPurchaseInfoQueryReq) {
        log.info("获取直发采购基本信息:{}", JSON.toJSONString(directPurchaseInfoQueryReq));
        DirectPurchaseInfo directPurchaseInfo =  directPurchaseInfoMapper.selectById(directPurchaseInfoQueryReq.getId());
        DirectPurchaseInfoQueryResp directPurchaseInfoQueryResp = new DirectPurchaseInfoQueryResp();
        if (!ObjectUtils.isEmpty(directPurchaseInfo)){
            directPurchaseInfoQueryResp = DirectPurchaseInfo2Resp.domain2VO(directPurchaseInfo);
        }
        log.info("返回直发采购基本信息:{}", JSON.toJSONString(directPurchaseInfoQueryResp));
        return DubboResponse.getOK(directPurchaseInfoQueryResp);
    }

    @Override
    public DubboResponse<PageInfo<OrderCompareCommonQueryResp>> listDirectPurchaseInfoByDeliveryDate(DeliveryDateQueryReq deliveryDateQueryReq) {
        log.info("根据配送时间查询直发采购单请求参数:{}", JSON.toJSONString(deliveryDateQueryReq));
        int totalCount = directPurchaseInfoMapper.countDataByDeliveryDate(deliveryDateQueryReq.getDeliveryTime());
        if (totalCount <= 0){
            return DubboResponse.getOK(PageInfo.emptyPageInfo());
        }
        PageInfo<OrderCompareCommonQueryResp> result = new PageInfo<>();
        result.setSize(totalCount);
        // ofc会先查询总数，pageSize参数会传0，这里直接返回可以少查询一次数据库
        if (deliveryDateQueryReq.getPageSize() <= 0){
            return DubboResponse.getOK(result);
        }
        List<DirectPurchaseInfo> directPurchaseInfos = directPurchaseInfoMapper.listDataByDeliveryDate(deliveryDateQueryReq.getDeliveryTime(),
                (deliveryDateQueryReq.getPageIndex() - 1) * deliveryDateQueryReq.getPageSize(), deliveryDateQueryReq.getPageSize());
        if (CollectionUtils.isEmpty(directPurchaseInfos)){
            return DubboResponse.getOK(result);
        }
        List<OrderItem> orderItemList = orderItemMapper.listDataByDeliveryDateAndStatusList(directPurchaseInfos.stream().map(DirectPurchaseInfo::getOrderNo).collect(Collectors.toList()));
        Map<String, List<OrderItem>> orderItemMap = orderItemList.stream().collect(Collectors.groupingBy(OrderItem::getOrderNo));
        result = new PageInfo<>(DeliveryPlan2Resp.buildDirectPurchaseDataList(directPurchaseInfos, orderItemMap));
        log.info("根据配送时间查询直发采购单请求结果:{}", JSON.toJSONString(result));
        return DubboResponse.getOK(result);
    }
}
