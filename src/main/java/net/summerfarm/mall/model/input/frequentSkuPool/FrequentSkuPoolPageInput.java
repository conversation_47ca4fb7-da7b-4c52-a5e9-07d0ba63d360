package net.summerfarm.mall.model.input.frequentSkuPool;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/20 16:13
 * @PackageName:net.summerfarm.mall.model.input.frequentSkuPool
 * @ClassName: FrequentSkuPoolPageInput
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class FrequentSkuPoolPageInput extends BasePageInput implements Serializable {

    private static final long serialVersionUID = 7902224853573129275L;

    /**
     * 是否完成引导 0-否 1-是
     */
    private Integer completeTheGuidance;

    /**
     * 一级类目id
     */
    private List<Integer> firstCategoryIdList;
}
