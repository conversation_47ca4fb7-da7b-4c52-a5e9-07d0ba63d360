package net.summerfarm.mall.model.input;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description
 * @date 2023/10/17 18:27:54
 */
@Data
public class DeliveryFeeRuleQueryInput implements Serializable {

    private static final long serialVersionUID = -7538886350708455710L;

    /**
     * sku集合
     */
    @NotBlank(message = "sku不能为空！")
    private String sku;

    /**
     * 地址ID
     */
    private Long contactId;
}
