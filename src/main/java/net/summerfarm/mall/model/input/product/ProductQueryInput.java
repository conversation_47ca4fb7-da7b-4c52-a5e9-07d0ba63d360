package net.summerfarm.mall.model.input.product;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

/**
 * @author: <EMAIL>
 * @create: 2023/10/25
 */
@Data
public class ProductQueryInput extends BasePageInput implements Serializable {

    /**
     * sku集合
     */
    private Set<String> skuQueryList;

    /**
     *  运营服务区域编号
     */
    private Integer areaNo;

    private List<SearchConditionInput> conditionInputs;
    /**
     * 商品title , 用来作为纠错功能查询
     */
    private String titleSuggest;
    /**
     * 最高价
     */
    private BigDecimal maxPrice;
    /**
     * 最低价
     */
    private BigDecimal minPrice;

    /**
     * 排序方式 1=综合、2=价格
     */
    private Integer sortBy;

    /**
     * 排序 1=升序、2=降序
     */
    private Integer sortDirection;
    /**
     * 仅看有货
     */
    private Integer saleOut;
}
