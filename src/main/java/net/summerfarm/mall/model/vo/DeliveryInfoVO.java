package net.summerfarm.mall.model.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2022/7/12
 */
@Data
public class DeliveryInfoVO implements Serializable {

    /**
     * 运费
     */
    private BigDecimal deliveryFee;

    /**
     * 金额免邮规则
     */
    private List<AmountRule> amountRules;

    /**
     * 件数免邮规则
     */
    private List<NumberRule> numberRules;

    /**
     * 固定星期免配送费 1-7 用","分隔
     */
    private String freeDeliveryWeek;

    /**
     * 金额规则
     */
    @Data
    public static class AmountRule implements Serializable {

        private BigDecimal price;

        private Integer type;

        /**
         * 非乳制品总金额
         */
        private BigDecimal fruitPrice;

        /**
         * 乳制品总金额
         */
        private BigDecimal dairyPrice;

        /**
         * 总金额
         */
        private BigDecimal totalPrice;

        /**
         * 自营总金额
         */
        private BigDecimal selfSupportPrice;

        /**
         * 代仓总金额
         */
        private BigDecimal agentPrice;
    }

    /**
     * 数量规则
     */
    @Data
    public static class NumberRule implements Serializable {

        private Integer num;

        private Integer type;

        /**
         * 非乳制品总件数
         */
        private Integer fruitNum;

        /**
         * 乳制品总件数
         */
        private Integer dairyNum;

        /**
         * 总件数
         */
        private Integer totalNum;

        /**
         * 自营总件数
         */
        private Integer selfSupportNum;

        /**
         * 代仓总件数
         */
        private Integer agentNum;

    }

//    /**
//     * 免邮日规则
//     */
//    class FreeDayRule implements Serializable {
//
//        /**
//         * 固定星期免配送费 1-7 用","分隔
//         */
//        private String freeDeliveryWeek;
//    }

}
