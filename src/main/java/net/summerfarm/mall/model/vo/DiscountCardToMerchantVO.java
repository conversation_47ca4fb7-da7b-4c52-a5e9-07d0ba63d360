package net.summerfarm.mall.model.vo;

import lombok.Data;
import net.summerfarm.mall.model.domain.DiscountCardToMerchant;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-05-15
 * @description
 */
@Data
public class DiscountCardToMerchantVO extends DiscountCardToMerchant {
    /**
     * 优惠卡名称
     */
    private String name;

    /**
     * 优惠卡金额
     */
    private BigDecimal discount;
}
