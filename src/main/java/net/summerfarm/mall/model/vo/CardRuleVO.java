package net.summerfarm.mall.model.vo;

import lombok.Data;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.mall.model.domain.CardRule;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
public class CardRuleVO extends CardRule{
    private String name;

    private Integer type;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime vaildDate;

    private Integer vaildTime;

    private Integer times;

    @NotNull(groups = {Add.class},message = "param.not.null.illegal")
    private Integer cardType;
}
