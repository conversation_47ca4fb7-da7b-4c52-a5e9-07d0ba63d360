package net.summerfarm.mall.repository;

import net.summerfarm.mall.common.config.DynamicConfig;
import net.summerfarm.mall.facade.inventory.ProductCostQueryFacade;
import net.summerfarm.mall.mapper.InventoryMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/1/16 14:10
 */

@Component
public class CycleInventoryCostRepository {

    @Resource
    private DynamicConfig dynamicConfig;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private ProductCostQueryFacade productCostQueryFacade;

    /**
     * 获取成本价
     * @param sku
     * @param warehouseNo
     * @return
     */
    public BigDecimal selectCycleCost(String sku, Integer warehouseNo){
        BigDecimal costPrice = null;
        if(dynamicConfig.getMallNewCostPriceSwitch()){
            costPrice = productCostQueryFacade.selectCycleCost(sku, warehouseNo);
        } else {
            costPrice = inventoryMapper.selectCycleCost(sku, warehouseNo);
        }
        return costPrice;
    }
}
