package net.summerfarm.mall.service.facade.dto;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/21 17:56
 * @PackageName:net.summerfarm.mall.service.facade.dto
 * @ClassName: DeliveryRuleDTO
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class DeliveryRuleDTO {

    /**
     * 配送截单日期时间
     */
    private LocalDateTime deliveryCloseTime;

    /**
     * 可配送配送时间
     */
    private List<LocalDate> deliveryTimes;
}
