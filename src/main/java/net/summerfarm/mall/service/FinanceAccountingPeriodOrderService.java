package net.summerfarm.mall.service;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description 账期订单
 * @date 2023/4/23 18:41:28
 */
public interface FinanceAccountingPeriodOrderService {

    /**
     * @description: 根据门店编号查询是否有未完结的账单
     * @author: lzh
     * @date: 2023/4/23 18:45
     * @param: [mId]
     * @return: java.lang.Integer
     **/
    Integer getUnFinishedCount(Long mId);
}
