package net.summerfarm.mall.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.enums.*;
import net.summerfarm.mall.mapper.ContactMapper;
import net.summerfarm.mall.model.domain.Contact;
import net.summerfarm.mall.model.dto.delivery.*;
import net.summerfarm.mall.model.input.DistributionRulesGetInput;
import net.summerfarm.mall.model.vo.DeliveryPlanVO;
import net.summerfarm.mall.model.vo.DistributionRulesVO;
import net.summerfarm.mall.service.DeliveryPlanService;
import net.summerfarm.mall.service.DistributionRulesService;
import net.summerfarm.mall.service.facade.DistributionRulesFacade;
import net.summerfarm.mall.service.facade.WmsAreaStoreFacade;
import net.summerfarm.mall.service.facade.WncDeliveryAlertQueryFacade;
import net.summerfarm.mall.service.facade.dto.*;
import net.xianmu.marketing.center.client.freight.req.DistributionRulesDetailReq;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description
 * @date 2023/10/9 17:33:21
 */
@Service
@Slf4j
public class DistributionRulesServiceImpl implements DistributionRulesService {

    @Resource
    private DistributionRulesFacade distributionRulesFacade;

    @Resource
    @Lazy
    private WmsAreaStoreFacade wmsAreaStoreFacade;

    @Resource
    private ContactMapper contactMapper;

    @Resource
    private WncDeliveryAlertQueryFacade wncDeliveryAlertQueryFacade;

    @Resource
    @Lazy
    private DeliveryPlanService deliveryPlanService;

    @Override
    public DistributionRulesDTO getInfo(DistributionRulesQueryDTO queryDTO) {
       //门店是否设置运费？ 门店管理运费规则 : （品牌是否设置运费？ 品牌管理运费规则 : 获取运营服务区域运费规则）
        if (Objects.isNull(queryDTO)) {
            return null;
        }
        DistributionRulesDetailReq rulesDetailReq = new DistributionRulesDetailReq();
        List<DistributionRulesDetailReq.DetailReq> detailReqList = new ArrayList<>();
        if (Objects.nonNull(queryDTO.getContactId())) {
            DistributionRulesDetailReq.DetailReq detailReq = new DistributionRulesDetailReq.DetailReq();
            detailReq.setTypeId(queryDTO.getContactId());
            detailReq.setType(DistributionRulesTypeEnum.MERCHANT.getCode());
            detailReqList.add(detailReq);
        }

        if (Objects.nonNull(queryDTO.getAdminId()) && Objects.nonNull(queryDTO.getAreaNo())) {
            DistributionRulesDetailReq.DetailReq detailReq = new DistributionRulesDetailReq.DetailReq();
            detailReq.setTypeId(queryDTO.getAdminId());
            detailReq.setType(DistributionRulesTypeEnum.ADMIN.getCode());
            detailReq.setAreaNo(queryDTO.getAreaNo());
            detailReqList.add(detailReq);
        }

        if (Objects.nonNull(queryDTO.getAreaNo())) {
            DistributionRulesDetailReq.DetailReq detailReq = new DistributionRulesDetailReq.DetailReq();
            detailReq.setTypeId(queryDTO.getAreaNo().longValue());
            detailReq.setType(DistributionRulesTypeEnum.AREA.getCode());
            detailReqList.add(detailReq);
        }

        if (CollectionUtils.isEmpty(detailReqList)) {
            return null;
        }

        rulesDetailReq.setDetailReqs(detailReqList);
        DistributionRulesDTO rulesDTOMap = distributionRulesFacade.getDetail(rulesDetailReq);
        return rulesDTOMap;
    }

    @Override
    public List<DistributionRulesVO> getDistributionRulesBySkus(DistributionRulesGetInput input) {
        Long contactId = input.getContactId();
        List<String> skuList = input.getSkuList();
        Long mId = RequestHolder.getMId();

        //获取默认地址
        Contact contact;
        if (Objects.isNull(contactId)) {
            contact = contactMapper.selectIsDefaultByMid(mId);
        } else {
            contact = contactMapper.selectByPrimaryKey(contactId);
        }

        if (Objects.isNull(contact)) {
            log.warn("DistributionRulesServiceImpl[]getDistributionRules[]contact[]error!");
            return null;
        }
        if (Objects.isNull(contactId)) {
            contactId = contact.getContactId();
        }

        //获取配送日期
        Map<String, AreaStoreQueryRes> areaStoreQueryRes = getAreaStoreQueryRes(contactId, mId, skuList);
        if (CollectionUtils.isEmpty(areaStoreQueryRes)) {
            log.warn("DistributionRulesServiceImpl[]getDistributionRules[]areaStoreQueryRes[]error!input:{}", JSON.toJSONString(input));
            return null;
        }

        //获取当前用户地址的所有待配送计划
        List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanService.getAllDeliveryPlanByMIdAndContactId(mId, contactId);
        Map<LocalDate, List<DeliveryPlanVO>> localDateListMap = deliveryPlanVOS.stream().collect(Collectors.groupingBy(DeliveryPlanVO::getDeliveryTime));

        //获取配送规则
        Map<Integer, List<RulesDTO>> map = null;
        Map<Integer, List<DeliveryFeeRuleInfoDTO>> listMap = null;
        if (Objects.equals(input.getDeliveryRulesType(), CommonStatus.YES.getCode())) {
            DeliveryFeeRuleReq deliveryFeeRuleReq = new DeliveryFeeRuleReq();
            deliveryFeeRuleReq.setContactId(contactId.intValue());
            deliveryFeeRuleReq.setAreaNo(RequestHolder.getMerchantAreaNo());
            deliveryFeeRuleReq.setXmAdminId(Optional.ofNullable(RequestHolder.getAdminId()).orElse(null));
            deliveryFeeRuleReq.setAddress(contact.getAddress());
            deliveryFeeRuleReq.setCity(contact.getCity());
            deliveryFeeRuleReq.setArea(contact.getArea());
            deliveryFeeRuleReq.setProvince(contact.getProvince());
            List<DeliveryFeeRuleInfoDTO> deliveryFeeRuleInfoDTOS = distributionRulesFacade.queryDeliveryFeeRule(deliveryFeeRuleReq);
            if (!CollectionUtils.isEmpty(deliveryFeeRuleInfoDTOS)) {
                listMap = deliveryFeeRuleInfoDTOS.stream().collect(Collectors.groupingBy(DeliveryFeeRuleInfoDTO::getAgeing));
            }
        } else {
            DistributionRulesQueryDTO distributionRulesQueryDTO = new DistributionRulesQueryDTO();
            distributionRulesQueryDTO.setContactId(contactId);
            distributionRulesQueryDTO.setAreaNo(RequestHolder.getMerchantAreaNo());
            if (RequestHolder.isMajor()) {
                distributionRulesQueryDTO.setAdminId(RequestHolder.getAdminId() == null ? null : RequestHolder.getAdminId().longValue());
            }
            DistributionRulesDTO info = this.getInfo(distributionRulesQueryDTO);
            if (!Objects.isNull(info) && !CollectionUtils.isEmpty(info.getRulesDTOS())) {
                map = info.getRulesDTOS().stream().collect(Collectors.groupingBy(RulesDTO::getAgeing));
            }
        }

        //获取预计送达时间
        DeliveryAlertReq deliveryAlertReq = new DeliveryAlertReq();
        deliveryAlertReq.setContactId(contact.getContactId());
        deliveryAlertReq.setCity(contact.getCity());
        deliveryAlertReq.setArea(contact.getArea());
        deliveryAlertReq.setStoreNo(contact.getStoreNo());
        deliveryAlertReq.setMerchantId(contact.getmId().toString());
        if (RequestHolder.isMajor()) {
            deliveryAlertReq.setAdminId(RequestHolder.getAdminId() == null ? null : RequestHolder.getAdminId().toString());
        }
        DeliveryAlertRes deliveryAlertRes = wncDeliveryAlertQueryFacade.queryDeliveryAlert(deliveryAlertReq);

        //组装数据
        List<DistributionRulesVO> distributionRulesVOS = new ArrayList<>(skuList.size());
        for (String sku : skuList) {
            AreaStoreQueryRes queryRes = areaStoreQueryRes.get(sku);
            if (Objects.isNull(queryRes) || Objects.isNull(queryRes.getDeliveryTime())) {
                continue;
            }
            LocalDate deliveryTime = queryRes.getDeliveryTime();
            Integer isEveryDayFlag = queryRes.getIsEveryDayFlag();
            DistributionRulesVO distributionRulesVO = new DistributionRulesVO();
            distributionRulesVO.setSku(sku);
            distributionRulesVO.setDeliveryTime(deliveryTime);
            distributionRulesVO.setCloseTime(queryRes.getCloseTime());
            distributionRulesVO.setDeliveryCloseTime(queryRes.getDeliveryCloseTime());
            distributionRulesVO.setLastDeliveryTime(deliveryAlertRes.getLastDeliveryTime());
            distributionRulesVO.setIsDeliveryPlan(Boolean.FALSE);
            distributionRulesVO.setShipmentDate(deliveryTime.minusDays(1L));
            distributionRulesVOS.add(distributionRulesVO);
            if (!CollectionUtils.isEmpty(localDateListMap) && localDateListMap.containsKey(deliveryTime)) {
                distributionRulesVO.setIsDeliveryPlan(Boolean.TRUE);
            }

            //1、判断日配或者非日配 假如非日配获取T+N配送规则
            //2、假如为日配 则根据商品属性来判断 商品经销：T+1 商品代销不入仓：T+N
            if (Objects.equals(input.getDeliveryRulesType(), CommonStatus.YES.getCode())) {
                if (InventoryEnums.SubType.SELF_NOT_INTO_WAREHOUSE.getSubType().equals(queryRes.getSkuSubType())) {
                    isEveryDayFlag = DeliveryEveryDayEnum.NON_DAYCARE.getCode();
                }
                if (CollectionUtils.isEmpty(listMap) || !listMap.containsKey(isEveryDayFlag) || CollectionUtils.isEmpty(listMap.get(isEveryDayFlag))) {
                    log.warn("DistributionRulesServiceImpl[]getDistributionRules[]deliveryFeeRuleInfoDTOS is null, sku:{}, queryRes:{}", sku, JSON.toJSONString(queryRes));
                    continue;
                }
                distributionRulesVO.setDeliveryFeeRuleInfoDTOS(listMap.get(isEveryDayFlag).get(0));
            } else {
                if (CollectionUtils.isEmpty(map)) {
                    log.warn("DistributionRulesServiceImpl[]getDistributionRules[]map is null, sku:{}, queryRes:{}", sku, JSON.toJSONString(queryRes));
                    continue;
                }
                getDistributionRules(map, queryRes, distributionRulesVO);
            }
        }
        return distributionRulesVOS;
    }

    private void getDistributionRules(Map<Integer, List<RulesDTO>> map, AreaStoreQueryRes queryRes, DistributionRulesVO distributionRulesVO) {
        if (DeliveryEveryDayEnum.DAYCARE.getCode().equals(queryRes.getIsEveryDayFlag())) {
            if (InventoryEnums.SubType.SELF_NOT_INTO_WAREHOUSE.getSubType().equals(queryRes.getSkuSubType())) {
                //T+N
                List<RulesDTO> rulesDTOS = map.get(CommonStatus.YES.getCode());
                if (!CollectionUtils.isEmpty(rulesDTOS)) {
                    sortCondition(rulesDTOS, distributionRulesVO);
                }
            } else {
                //T+1
                List<RulesDTO> rulesDTOS = map.get(CommonStatus.NO.getCode());
                if (!CollectionUtils.isEmpty(rulesDTOS)) {
                    sortCondition(rulesDTOS, distributionRulesVO);
                }
            }
        } else if (DeliveryEveryDayEnum.NON_DAYCARE.getCode().equals(queryRes.getIsEveryDayFlag())) {
            //T+N
            List<RulesDTO> rulesDTOS = map.get(CommonStatus.YES.getCode());
            if (!CollectionUtils.isEmpty(rulesDTOS)) {
                sortCondition(rulesDTOS, distributionRulesVO);
            }
        } else {
            log.warn("DistributionRulesServiceImpl[]getDistributionRules[]isEveryDayFlag[]error!queryRes:{}", JSON.toJSONString(queryRes));
        }
    }

    /**
     * @description: 获取库存信息
     * @author: lzh
     * @date: 2023/10/11 15:21
     * @param: [contactId, mId, skus]
     * @return: java.util.List<net.summerfarm.mall.service.facade.dto.AreaStoreQueryRes>
     **/
    private Map<String, AreaStoreQueryRes> getAreaStoreQueryRes(Long contactId, Long mId, List<String> skus) {
        AreaStoreQueryReq areaStoreQueryReq = new AreaStoreQueryReq();
        areaStoreQueryReq.setContactId(contactId);
        areaStoreQueryReq.setSkuCodeList(skus);
        areaStoreQueryReq.setMId(mId);
        areaStoreQueryReq.setSource(DistOrderSourceEnum.getDistOrderSource(RequestHolder.getBusinessLine()));
        Map<String, AreaStoreQueryRes> storeQueryResMap = wmsAreaStoreFacade.getInfo(areaStoreQueryReq);
        return storeQueryResMap;
    }

    /**
     * @description: //配送规则明细排序 全部 》 非乳制品 》 乳制品
     * @author: lzh
     * @date: 2023/10/18 18:28
     * @param: [rulesDTOS, e]
     * @return: void
     **/
    private void sortCondition(List<RulesDTO> rulesDTOS, DistributionRulesVO distributionRulesVO) {
        List<ConditionsDTO> conditionsDTOS = rulesDTOS.get(0).getConditionsDTOS();
        if (CollectionUtils.isEmpty(conditionsDTOS)) {
            distributionRulesVO.setConditionsDTOS(null);
        } else {
            List<ConditionsDTO> sortConditionsDTOS = new ArrayList<>(conditionsDTOS.size());
            List<ConditionsDTO> all = conditionsDTOS.stream().filter(conditionsDTO -> conditionsDTO.getProductType()
                    .equals(DistributionRulesProductTypeEnum.ALL.getCode())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(all)) {
                sortConditionsDTOS.addAll(all);
            }
            List<ConditionsDTO> nonDairy = conditionsDTOS.stream().filter(conditionsDTO -> conditionsDTO.getProductType()
                    .equals(DistributionRulesProductTypeEnum.NON_DAIRY.getCode())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(nonDairy)) {
                sortConditionsDTOS.addAll(nonDairy);
            }
            List<ConditionsDTO> lactation = conditionsDTOS.stream().filter(conditionsDTO -> conditionsDTO.getProductType()
                    .equals(DistributionRulesProductTypeEnum.LACTATION.getCode())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(lactation)) {
                sortConditionsDTOS.addAll(lactation);
            }
            distributionRulesVO.setConditionsDTOS(sortConditionsDTOS);
        }
    }
}
