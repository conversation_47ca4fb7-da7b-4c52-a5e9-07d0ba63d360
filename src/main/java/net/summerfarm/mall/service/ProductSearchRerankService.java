package net.summerfarm.mall.service;

import com.github.pagehelper.PageInfo;

import net.summerfarm.mall.engine.client.req.EsHomeProductQueryConditionReq;
import net.summerfarm.mall.engine.client.req.EsHomeProductQueryReq;
import net.summerfarm.mall.facade.engine.model.EsMarketItemInfoDTO;
import net.summerfarm.mall.model.dto.product.MallProductQueryDTO;

public interface ProductSearchRerankService {
    /**
     * 根据用户实验分组和类目预测对商品进行重排序。
     *
     * @param esHomeProductQueryReq 商品查询请求，包含查询关键字、分页信息等。
     * @param conditionReq          查询条件，包含过滤条件等。
     * @param mallProductQuery      商城商品查询请求，包含配送时间等信息。
     * @param withScore             是否需要返回ES的分数，如果为true，则会对返回的商品列表进行过滤，只保留分数大于等于最大分数一定比例的商品。
     * @return 重排序后的商品分页信息，包含商品列表和分页信息。
     */
    PageInfo<EsMarketItemInfoDTO> searchProducts(EsHomeProductQueryReq esHomeProductQueryReq, EsHomeProductQueryConditionReq conditionReq, MallProductQueryDTO mallProductQuery, boolean withScore);
}
