package net.summerfarm.mall.task;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.common.schedulerx.JobNameConstant;
import net.summerfarm.mall.common.schedulerx.JobParameters;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.summerfarm.mall.service.OrderService;
import net.summerfarm.mall.wechat.token.TokenService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 分布式调度任务
 * @createTime 2021年11月30日 13:13:00
 */
@Component
@Slf4j
public class SchedulerxTask extends XianMuJavaProcessorV2 {

    @Resource
    private TokenService tokenService;
    @Resource
    private OrderService orderService;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;


    @Override
    public ProcessResult processResult(XmJobInput jobContext) throws Exception {
        log.info("分布式调度任务开始------调度任务:{}",jobContext.getJobParameters());

        String jobParametersStr=jobContext.getJobParameters();
        if(StringUtils.isBlank(jobParametersStr)){
            log.error("未找到分布式调度任务------");
        }
        JobParameters jobParameters = JSONObject.parseObject(jobParametersStr, JobParameters.class);
        if(Objects.isNull(jobParameters) || StringUtils.isBlank(jobParameters.getJobName())){
            log.error("未找到分布式调度任务------");
        }

        if(Objects.nonNull(jobParameters) && StringUtils.isNotBlank(jobParameters.getJobName())){
            // 刷新微信token
            if(JobNameConstant.WECHAT_TOKEN_REFRESH_JOB_NAME.equals(jobParameters.getJobName())){
                tokenService.tokenRefresh();
            }

            if(JobNameConstant.ADD_MQ_DELAY.equals(jobParameters.getJobName())){
                //加载未支付订单至延迟队列 取消队列&boc支付反查队列
                orderService.unpaidOrders();
                //加载未支付虚拟商品订单
                orderService.unpaidVirtualOrders();
            }
        }

        log.info("分布式调度任务结束------调度任务:{}",jobContext.getJobParameters());
        return new ProcessResult(true);
    }
}
