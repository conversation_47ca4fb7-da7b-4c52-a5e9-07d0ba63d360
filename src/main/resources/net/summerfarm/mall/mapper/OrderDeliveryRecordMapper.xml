<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.OrderDeliveryRecordMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.OrderDeliveryRecord" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="add_time" property="addTime" jdbcType="DATE" />
        <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL" />
        <result column="delivery_rule" property="deliveryRule" jdbcType="VARCHAR" />
        <result column="delivery_free_Day" property="deliveryFreeDay" jdbcType="VARCHAR" />
        <result column="big_merchant_delivery_rule" property="bigMerchantDeliveryRule" jdbcType="VARCHAR" />
        <result column="big_merchant_delivery_fee" property="bigMerchantDeliveryFee" jdbcType="DECIMAL" />
        <result column="delivery_card_id" property="deliveryCardID" jdbcType="VARCHAR" />
        <result column="delivery_coupon_id" property="deliveryCouponID" jdbcType="VARCHAR" />
        <result column="pay_delivery_fee" property="payDeliveryFee" jdbcType="DECIMAL" />

    </resultMap>

    <insert id="insertRecord" parameterType="net.summerfarm.mall.model.domain.OrderDeliveryRecord">
        insert into order_delivery_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="deliveryFee != null">
                delivery_fee,
            </if>
            <if test="deliveryRule != null">
                delivery_rule,
            </if>
            <if test="deliveryFreeDay != null">
                delivery_free_Day,
            </if>
            <if test="bigMerchantDeliveryRule != null">
                big_merchant_delivery_rule,
            </if>
            <if test="bigMerchantDeliveryFee != null">
                big_merchant_delivery_fee,
            </if>
            <if test="deliveryCardID != null">
                delivery_card_id,
            </if>
            <if test="deliveryCouponID != null">
                delivery_coupon_id,
            </if>
            <if test="payDeliveryFee != null">
                pay_delivery_fee,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">
                #{orderNo},
            </if>
            <if test="addTime != null">
                #{addTime},
            </if>
            <if test="deliveryFee != null">
                #{deliveryFee},
            </if>
            <if test="deliveryRule != null">
                #{deliveryRule},
            </if>
            <if test="deliveryFreeDay != null">
                #{deliveryFreeDay},
            </if>
            <if test="bigMerchantDeliveryRule != null">
                #{bigMerchantDeliveryRule},
            </if>
            <if test="bigMerchantDeliveryFee != null">
                #{bigMerchantDeliveryFee},
            </if>
            <if test="deliveryCardID != null">
                #{deliveryCardID},
            </if>
            <if test="deliveryCouponID != null">
                #{deliveryCouponID},
            </if>
            <if test="payDeliveryFee != null">
                #{payDeliveryFee},
            </if>
        </trim>
    </insert>

    <select id="queryOrderRecord" resultMap="BaseResultMap">
        select id,order_no,add_time,delivery_fee,delivery_rule,delivery_free_Day,
        big_merchant_delivery_rule,big_merchant_delivery_fee,delivery_card_id,delivery_card_id,delivery_coupon_id,pay_delivery_fee
        from order_delivery_record
          where order_no = #{orderNo}
    </select>
    <select id="selectCouponDeliveryFeeByOrderNo" resultType="java.math.BigDecimal">
        SELECT IFNULL(c.money,0) money  FROM `order_delivery_record`  odr
        left join merchant_coupon mc
        on odr.`delivery_coupon_id`=mc.id
        left join `coupon` c
        on mc.`coupon_id` =c.id
        where odr.`order_no` =#{orderNo}
    </select>

    <select id="selectDeliveryFeeByOrderNo" resultType="java.math.BigDecimal">
        SELECT IFNULL(odr.pay_delivery_fee,0) money  FROM `order_delivery_record`  odr
        left join merchant_coupon mc
        on odr.`delivery_coupon_id`=mc.id
        left join `coupon` c
        on mc.`coupon_id` =c.id
        where odr.`order_no` =#{orderNo}
    </select>
</mapper>