<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.OrderOuterInfoMapper" >


    <select id="queryOrderOuterInfo" resultType="net.summerfarm.mall.model.domain.OrderOuterInfo">
        select delivery_date deliveryDate,amount,m_id mId
        from order_outer_info
        where order_no =#{orderNo} and outer_platform_id = #{outerPlatformId}
    </select>
    <select id="queryInfoByXmOrderNo" resultType="net.summerfarm.mall.model.domain.OrderOuterInfo">
        select id,order_no orderNo,m_id mId,gmt_create gmtCreate,outer_platform_id outerPlatformId,xm_order_no xmOrderNo
         from order_outer_info
        where xm_order_no = #{xmOrderNo}
    </select>
    <select id="selectOrderOuterItem" resultType="net.summerfarm.mall.model.domain.OrderOuterItem">
                select
        id,order_no orderNo,sku,amount,standard,unit,name,xm_sku xmSku,pd_name pdName,outer_platform_id outerPlatformId,item_id itemId
        from order_outer_item
        where order_no = #{orderNo} and outer_platform_id = #{outerPlatformId}
    </select>

    <update id="updateOrderOuterInfo" parameterType="net.summerfarm.mall.model.domain.OrderOuterInfo">
        update order_outer_info set xm_order_no = #{xmOrderNo},status = #{status},order_success_time = now()
        where order_no  =#{orderNo} and outer_platform_id = #{outerPlatformId}
    </update>

</mapper>