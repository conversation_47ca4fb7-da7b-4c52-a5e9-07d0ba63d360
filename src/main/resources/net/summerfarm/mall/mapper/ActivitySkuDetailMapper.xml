<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.ActivitySkuDetailMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.market.ActivitySkuDetail">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="item_config_id" jdbcType="BIGINT" property="itemConfigId"/>
    <result column="sku" jdbcType="VARCHAR" property="sku"/>
    <result column="rounding_mode" jdbcType="TINYINT" property="roundingMode"/>
    <result column="adjust_type" jdbcType="TINYINT" property="adjustType"/>
    <result column="amount" jdbcType="DECIMAL" property="amount"/>
    <result column="sort" jdbcType="INTEGER" property="sort"/>
    <result column="plan_quantity" jdbcType="INTEGER" property="planQuantity"/>
    <result column="actual_quantity" jdbcType="INTEGER" property="actualQuantity"/>
    <result column="lock_quantity" jdbcType="INTEGER" property="lockQuantity"/>
    <result column="account_limit" jdbcType="TINYINT" property="accountLimit"/>
    <result column="limit_quantity" jdbcType="INTEGER" property="limitQuantity"/>
    <result column="min_sale_num" jdbcType="INTEGER" property="minSaleNum"/>
    <result column="single_deposit" jdbcType="DECIMAL" property="singleDeposit"/>
    <result column="expansion_ratio" jdbcType="DECIMAL" property="expansionRatio"/>
    <result column="hide_price" jdbcType="TINYINT" property="hidePrice"/>
    <result column="timing_config" jdbcType="VARCHAR" property="timingConfig"/>
    <result column="is_support_timing" jdbcType="TINYINT" property="isSupportTiming"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
    <result column="discount_label" jdbcType="TINYINT" property="discountLabel"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <resultMap id="resultMapForDetailDTO"
    type="net.summerfarm.mall.model.dto.market.activity.ActivitySkuDetailDTO">
    <result column="basicInfoId" jdbcType="BIGINT" property="basicInfoId"/>
    <result column="name" jdbcType="VARCHAR" property="activityName"/>
    <result column="item_config_id" jdbcType="BIGINT" property="itemConfigId"/>
    <result column="sku" jdbcType="VARCHAR" property="sku"/>
    <result column="rounding_mode" jdbcType="TINYINT" property="roundingMode"/>
    <result column="adjust_type" jdbcType="TINYINT" property="adjustType"/>
    <result column="amount" jdbcType="DECIMAL" property="amount"/>
    <result column="sort" jdbcType="INTEGER" property="sort"/>
    <result column="plan_quantity" jdbcType="INTEGER" property="planQuantity"/>
    <result column="actual_quantity" jdbcType="INTEGER" property="actualQuantity"/>
    <result column="lock_quantity" jdbcType="INTEGER" property="lockQuantity"/>
    <result column="account_limit" jdbcType="TINYINT" property="accountLimit"/>
    <result column="limit_quantity" jdbcType="INTEGER" property="limitQuantity"/>
    <result column="min_sale_num" jdbcType="INTEGER" property="minSaleNum"/>
    <result column="single_deposit" jdbcType="DECIMAL" property="singleDeposit"/>
    <result column="expansion_ratio" jdbcType="DECIMAL" property="expansionRatio"/>
    <result column="hide_price" jdbcType="TINYINT" property="hidePrice"/>
    <result column="timing_config" jdbcType="VARCHAR" property="timingConfig"/>
    <result column="is_support_timing" jdbcType="TINYINT" property="isSupportTiming"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
    <result column="platform" jdbcType="INTEGER" property="platform"/>
    <result column="discount_label" jdbcType="TINYINT" property="discountLabel"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="activityCreateTime" jdbcType="TIMESTAMP" property="activityCreateTime"/>
    <result column="ladder_config" jdbcType="VARCHAR" property="ladderPrice"/>
  </resultMap>

  <sql id="Base_Column_List">
    `id`
    , `item_config_id`, `sku`, `rounding_mode`, `adjust_type`, `amount`, `sort`, `plan_quantity`,
    `actual_quantity`, `lock_quantity`, `account_limit`, `limit_quantity`, `min_sale_num`,
    `single_deposit`, `expansion_ratio`, `hide_price`, `timing_config`,`is_support_timing`, `del_flag`, `discount_label`,
    `create_time`,`update_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_sku_detail
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from activity_sku_detail
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.market.ActivitySkuDetail">
    insert into activity_sku_detail (`id`, `item_config_id`, `sku`, `rounding_mode`,
                                     `adjust_type`, `amount`, `sort`,
                                     `plan_quantity`, `actual_quantity`, `lock_quantity`,
                                     `account_limit`, `limit_quantity`, `min_sale_num`,
                                     `single_deposit`, `expansion_ratio`, `hide_price`,
                                     `timing_config`,`is_support_timing`, `del_flag`, `create_time`,
                                     `update_time`)
    values (#{id,jdbcType=BIGINT}, #{itemConfigId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, #{roundingMode,jdbcType=TINYINT}
            #{adjustType,jdbcType=TINYINT}, #{amount,jdbcType=DECIMAL}, #{sort,jdbcType=INTEGER},
            #{planQuantity,jdbcType=INTEGER}, #{actualQuantity,jdbcType=INTEGER},
            #{lockQuantity,jdbcType=INTEGER},
            #{accountLimit,jdbcType=TINYINT}, #{limitQuantity,jdbcType=INTEGER},
            #{minSaleNum,jdbcType=INTEGER},
            #{singleDeposit,jdbcType=DECIMAL}, #{expansionRatio,jdbcType=DECIMAL},
            #{hidePrice,jdbcType=TINYINT},
            #{timingConfig,jdbcType=VARCHAR}, #{isSupportTiming,jdbcType=TINYINT}, #{delFlag,jdbcType=TINYINT},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective"
    parameterType="net.summerfarm.mall.model.domain.market.ActivitySkuDetail"
    useGeneratedKeys="true" keyProperty="id">
    insert into activity_sku_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="itemConfigId != null">
        `item_config_id`,
      </if>
      <if test="sku != null">
        `sku`,
      </if>
      <if test="roundingMode != null">
        `rounding_mode`,
      </if>
      <if test="adjustType != null">
        `adjust_type`,
      </if>
      <if test="amount != null">
        `amount`,
      </if>
      <if test="sort != null">
        `sort`,
      </if>
      <if test="planQuantity != null">
        `plan_quantity`,
      </if>
      <if test="actualQuantity != null">
        `actual_quantity`,
      </if>
      <if test="lockQuantity != null">
        `lock_quantity`,
      </if>
      <if test="accountLimit != null">
        `account_limit`,
      </if>
      <if test="limitQuantity != null">
        `limit_quantity`,
      </if>
      <if test="minSaleNum != null">
        `min_sale_num`,
      </if>
      <if test="singleDeposit != null">
        `single_deposit`,
      </if>
      <if test="expansionRatio != null">
        `expansion_ratio`,
      </if>
      <if test="hidePrice != null">
        `hide_price`,
      </if>
      <if test="timingConfig != null">
        `timing_config`,
      </if>
      <if test="delFlag != null">
        `del_flag`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="itemConfigId != null">
        #{itemConfigId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="roundingMode != null">
        #{roundingMode,jdbcType=TINYINT},
      </if>
      <if test="adjustType != null">
        #{adjustType,jdbcType=TINYINT},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="planQuantity != null">
        #{planQuantity,jdbcType=INTEGER},
      </if>
      <if test="actualQuantity != null">
        #{actualQuantity,jdbcType=INTEGER},
      </if>
      <if test="lockQuantity != null">
        #{lockQuantity,jdbcType=INTEGER},
      </if>
      <if test="accountLimit != null">
        #{accountLimit,jdbcType=TINYINT},
      </if>
      <if test="limitQuantity != null">
        #{limitQuantity,jdbcType=INTEGER},
      </if>
      <if test="minSaleNum != null">
        #{minSaleNum,jdbcType=INTEGER},
      </if>
      <if test="singleDeposit != null">
        #{singleDeposit,jdbcType=DECIMAL},
      </if>
      <if test="expansionRatio != null">
        #{expansionRatio,jdbcType=DECIMAL},
      </if>
      <if test="hidePrice != null">
        #{hidePrice,jdbcType=TINYINT},
      </if>
      <if test="timingConfig != null">
        #{timingConfig,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="net.summerfarm.mall.model.domain.market.ActivitySkuDetail">
    update activity_sku_detail
    <set>
      <if test="itemConfigId != null">
        `item_config_id` = #{itemConfigId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        `sku` = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="roundingMode != null">
        rounding_mode = #{roundingMode,jdbcType=TINYINT},
      </if>
      <if test="adjustType != null">
        `adjust_type` = #{adjustType,jdbcType=TINYINT},
      </if>
      <if test="amount != null">
        `amount` = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="sort != null">
        `sort` = #{sort,jdbcType=INTEGER},
      </if>
      <if test="planQuantity != null">
        `plan_quantity` = #{planQuantity,jdbcType=INTEGER},
      </if>
      <if test="actualQuantity != null">
        `actual_quantity` = #{actualQuantity,jdbcType=INTEGER},
      </if>
      <if test="lockQuantity != null">
        `lock_quantity` = #{lockQuantity,jdbcType=INTEGER},
      </if>
      <if test="accountLimit != null">
        `account_limit` = #{accountLimit,jdbcType=TINYINT},
      </if>
      <if test="limitQuantity != null">
        `limit_quantity` = #{limitQuantity,jdbcType=INTEGER},
      </if>
      <if test="minSaleNum != null">
        `min_sale_num` = #{minSaleNum,jdbcType=INTEGER},
      </if>
      <if test="singleDeposit != null">
        `single_deposit` = #{singleDeposit,jdbcType=DECIMAL},
      </if>
      <if test="expansionRatio != null">
        `expansion_ratio` = #{expansionRatio,jdbcType=DECIMAL},
      </if>
      <if test="hidePrice != null">
        `hide_price` = #{hidePrice,jdbcType=TINYINT},
      </if>
      <if test="timingConfig != null">
        `timing_config` = #{timingConfig,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        `del_flag` = #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey"
    parameterType="net.summerfarm.mall.model.domain.market.ActivitySkuDetail">
    update activity_sku_detail
    set `item_config_id`  = #{itemConfigId,jdbcType=BIGINT},
        rounding_mode     = #{roundingMode,jdbcType=TINYINT},
        `sku`             = #{sku,jdbcType=VARCHAR},
        `adjust_type`     = #{adjustType,jdbcType=TINYINT},
        `amount`          = #{amount,jdbcType=DECIMAL},
        `sort`            = #{sort,jdbcType=INTEGER},
        `plan_quantity`   = #{planQuantity,jdbcType=INTEGER},
        `actual_quantity` = #{actualQuantity,jdbcType=INTEGER},
        `lock_quantity`   = #{lockQuantity,jdbcType=INTEGER},
        `account_limit`   = #{accountLimit,jdbcType=TINYINT},
        `limit_quantity`  = #{limitQuantity,jdbcType=INTEGER},
        `min_sale_num`    = #{minSaleNum,jdbcType=INTEGER},
        `single_deposit`  = #{singleDeposit,jdbcType=DECIMAL},
        `expansion_ratio` = #{expansionRatio,jdbcType=DECIMAL},
        `hide_price`      = #{hidePrice,jdbcType=TINYINT},
        `timing_config`   = #{timingConfig,jdbcType=VARCHAR},
        `del_flag`        = #{delFlag,jdbcType=TINYINT},
        `create_time`     = #{createTime,jdbcType=TIMESTAMP},
        `update_time`     = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <insert id="insertBatch" parameterType="net.summerfarm.mall.model.domain.market.ActivitySkuDetail"
    useGeneratedKeys="true" keyProperty="id">
    insert into activity_sku_detail (`item_config_id`, `sku`,rounding_mode
    `adjust_type`, `amount`, `sort`,
    `plan_quantity`, `actual_quantity`, `lock_quantity`,
    `account_limit`, `limit_quantity`, `min_sale_num`,
    `single_deposit`, `expansion_ratio`, `hide_price`,
    `timing_config`)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{configId,jdbcType=BIGINT},
      #{item.sku,jdbcType=VARCHAR},#{item.roundingMode,jdbcType=TINYINT},
      #{item.adjustType,jdbcType=TINYINT}, #{item.amount,jdbcType=DECIMAL},
      #{item.sort,jdbcType=INTEGER},
      #{item.planQuantity,jdbcType=INTEGER}, #{item.actualQuantity,jdbcType=INTEGER},
      #{item.lockQuantity,jdbcType=INTEGER},
      #{item.accountLimit,jdbcType=TINYINT}, #{item.limitQuantity,jdbcType=INTEGER},
      #{item.minSaleNum,jdbcType=INTEGER},
      #{item.singleDeposit,jdbcType=DECIMAL}, #{item.expansionRatio,jdbcType=DECIMAL},
      #{item.hidePrice,jdbcType=TINYINT},
      #{item.timingConfig,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="selectBySku" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_sku_detail
    where item_config_id = #{itemConfigId} and sku = #{sku} and del_flag = 0
  </select>

  <select id="selectByItemConfig" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_sku_detail
    where item_config_id = #{itemConfigId} and del_flag = 0
  </select>

  <update id="updateDelFlag">
    update activity_sku_detail
    set del_flag = 1
    where item_config_id = #{itemConfigId}
    <if test="sku != null">
      and sku = #{sku}
    </if>
    and del_flag = 0
  </update>

  <select id="listByInfoIdAndSku">
    select *
    from activity_item_config aic right join activity_sku_detail asd on aic.id = asd.item_config_id
    where aic.del_flag = 0 and asd.del_flag = 0
    and (aic.basic_info_id,asd.sku) in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      (#{item.basicInfoId},#{item.sku})
    </foreach>

  </select>

  <select id="listByItemConfigsAndSku" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_sku_detail
    where sku = #{sku} and del_flag = 0
    and item_config_id in
    <foreach collection="configList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="listByItemConfigsSkus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_sku_detail
    where del_flag = 0
    and sku in
    <foreach collection="skus" item="sku" open="(" separator="," close=")">
      #{sku}
    </foreach>
    and item_config_id in
    <foreach collection="configList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <update id="updateDelFlagBatch">
    update activity_sku_detail
    set del_flag = 1
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
    and del_flag = 0
  </update>

  <select id="listByBasicInfoIds" resultMap="resultMapForDetailDTO">
    select abi.id basicInfoId,abi.name, asd.*,ascc.platform,abi.create_time activityCreateTime
    from activity_basic_info abi
    left join activity_scene_config ascc on abi.id = ascc.basic_info_id
    left join activity_item_config aic on abi.id = aic.basic_info_id
    left join activity_sku_detail asd on aic.id = asd.item_config_id
    where
    abi.id in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>

    and sku = #{sku}
    and abi.del_flag = 0 and aic.del_flag = 0 and asd.del_flag = 0
  </select>

  <select id="listByBasicInfoIdsAndSkus" resultMap="resultMapForDetailDTO">
    select abi.id basicInfoId,abi.name, asd.*,ascc.platform,abi.create_time activityCreateTime
    from activity_basic_info abi
    left join activity_scene_config ascc on abi.id = ascc.basic_info_id
    left join activity_item_config aic on abi.id = aic.basic_info_id
    left join activity_sku_detail asd on aic.id = asd.item_config_id
    where
    abi.id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
    and sku in
    <foreach collection="skus" item="sku" open="(" separator="," close=")">
      #{sku}
    </foreach>
    and abi.del_flag = 0 and aic.del_flag = 0 and asd.del_flag = 0
  </select>


  <select id="selectByBasicInfoId" resultMap="resultMapForDetailDTO">
    select abi.id basicInfoId,abi.name, asd.*
    from activity_basic_info abi
    left join activity_item_config aic on abi.id = aic.basic_info_id
    left join activity_sku_detail asd on aic.id = asd.item_config_id
    where
    abi.id = #{basicInfoId}
    and sku = #{sku}
    and abi.del_flag = 0 and aic.del_flag = 0 and asd.del_flag = 0
  </select>

  <select id="listByItemConfigs" resultMap="resultMapForDetailDTO">
    select abi.id basicInfoId, asd.*
    from activity_basic_info abi
    left join activity_item_config aic on abi.id = aic.basic_info_id
    left join activity_sku_detail asd on aic.id = asd.item_config_id
    where asd.del_flag = 0
    and item_config_id in
    <foreach collection="configList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <update id="updateActivityStock">
    update activity_sku_detail
    set actual_quantity = actual_quantity - #{quantity},lock_quantity = lock_quantity + #{quantity}
    where item_config_id = #{itemConfigId} and sku =#{sku} and del_flag =0 and actual_quantity >= #{quantity}
  </update>

  <update id="returnActivityStock">
    update activity_sku_detail
    set actual_quantity = actual_quantity + #{quantity},lock_quantity = lock_quantity - #{quantity}
    where item_config_id = #{itemConfigId} and sku =#{sku} and lock_quantity >= #{quantity}
  </update>
</mapper>
