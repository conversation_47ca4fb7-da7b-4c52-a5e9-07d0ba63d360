<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.DeliveryPathMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.DeliveryPath">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="store_no" property="storeNo" jdbcType="INTEGER"/>
        <result column="delivery_time" property="deliveryTime"/>
        <result column="contact_id" property="contactId" jdbcType="BIGINT"/>
        <result column="time_frame" property="timeFrame" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="total_volume" property="totalVolume" jdbcType="DECIMAL"/>
        <result column="path" property="path" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="addtime" property="addtime"/>
        <result column="type" property="type"/>
        <result column="path_status" property="pathStatus"/>
        <result column="finish_poi" property="finishPoi"/>
        <result column="finish_poi_name" property="finishPoiName"/>
        <result column="finish_distance" property="finishDistance"/>
        <result column="delivery_pic" property="deliveryPic"/>
        <result column="finish_time" property="finishTime"/>
    </resultMap>

    <sql id="BaseColumn">
        id,store_no,delivery_time,contact_id,time_frame,remark,total_volume,path,sort,addtime,`type`,path_status,finish_poi,finish_poi_name,finish_distance,
        delivery_pic,finish_time
    </sql>


    <select id="selectOne" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM delivery_path
        WHERE contact_id = #{contactId}
        <if test="storeNo != null">
            AND store_no = #{storeNo}
        </if>
        AND delivery_time = #{deliveryTime}
        AND brand_type = 0
    </select>

    <select id="selectDeliveryPath" resultMap="BaseResultMap">
        select <include refid="BaseColumn"/>
        from delivery_path
        <where>
            <if test="storeNo != null">
                AND store_no = #{storeNo}
            </if>
            <if test="deliveryTime != null">
                AND delivery_time = #{deliveryTime}
            </if>
            <if test="path != null">
                AND path = #{path}
            </if>
            <if test="pathStatus!= null">
                AND path_status = #{pathStatus}
            </if>
        </where>
    </select>

</mapper>