<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.TrolleyMapper">
  <sql id="Base_Column_List">
    m_id, accountId, sku, product_type, quantity, `check`, update_time, del_flag, biz_id
  </sql>

  <update id="updateChecked2Del" parameterType="java.lang.Long" >
    UPDATE trolley t set t.del_flag =1 , t.update_time = now()
    WHERE t.`check` = 1
    AND t.m_id = #{mId}
    AND t.account_id = #{accountId}
  </update>

  <!-- 查询客户购物车中所有sku-->
  <select id="selectTrolleyAllSku" resultType="net.summerfarm.mall.model.domain.Trolley">
    SELECT m_id mId, account_id accountId, sku,parent_sku parentSku, suit_id suitId,product_type productType, quantity, `check`, del_flag delFlag, biz_id bizId
    FROM trolley t
    WHERE t.m_id = #{mId}
    AND t.account_id = #{accountId}
    <if test="check != null">
      AND t.`check` = #{check}
    </if>
    AND t.del_flag = 0
    order by t.update_time desc
  </select>
  <select id="selectTrolleyAllSkuNoParent" resultType="net.summerfarm.mall.model.domain.Trolley">
    SELECT m_id mId, account_id accountId, sku,parent_sku parentSku, suit_id suitId,product_type productType, quantity, `check`, del_flag delFlag, biz_id bizId
    FROM trolley t
    WHERE t.m_id = #{mId}
    AND t.account_id = #{accountId}
    AND t.parent_sku = "0"
    AND t.del_flag = 0
    AND t.product_type = 0
    order by t.update_time desc
  </select>
  <select id="select4cartOutTrolley" resultType="net.summerfarm.mall.model.vo.OrderItemVO">
    SELECT
      #{mId}                          mId,
      #{accountId}                    accountId,
      0                               productType,
      a.sku,
      p.pd_name                         pdName,
      p.category_id                     categoryId,
      a.on_sale                         onSale,
      if(a.share = 0,a.quantity,ar.online_quantity) stock,
      a.share,
      a.sales_mode                      salesMode,
      a.limited_quantity                limitedQuantity,
      i.maturity,
      i.weight,
      i.base_sale_quantity              baseSaleQuantity,
      i.base_sale_unit                  baseSaleUnit,
      c.type,
      ifnull(i.sku_pic, p.picture_path) picturePath,
      p.storage_location                storageLocation,
      a.ladder_price                    ladderPrice,
      p.pd_id                           pdId,
      IFNULL(a.price, i.sale_price)     salePrice,
      a.price                           originalPrice,
      i.volume,
      i.weight_num                      weightNum,
      i.type                            skuType,
      if(c.type = 4, null, a.info)      qualityInfo
      <if test="adminId != null">
        ,mp.price price,mp.direct, mp.pay_method payMethod, a.sale_price activityOriginPrice
      </if>
    FROM area_sku a
    LEFT JOIN inventory i on a.sku = i.sku
    LEFT JOIN products p on p.pd_id = i.pd_id
    LEFT JOIN category c on c.id = p.category_id
    LEFT JOIN area area on a.area_no = area.area_no
    LEFT join (
        select area_no,store_no from fence where area_no = #{areaNo} and status = 0 limit  1
    ) f on f.area_no = a.area_no
    INNER JOIN warehouse_inventory_mapping wim on wim.store_no = f.store_no and wim.sku = i.sku
    INNER JOIN area_store ar on ar.sku=wim.sku and wim.warehouse_no = ar.area_no
    <if test="adminId != null">
      LEFT JOIN major_price mp on mp.sku=a.sku and mp.area_no=a.area_no and mp.admin_id=#{adminId} and mp.direct=#{direct}
      and mp.valid_time <![CDATA[<=]]> now() and  mp.invalid_time <![CDATA[>]]> now()
    </if>
    WHERE a.area_no = #{areaNo}
    and i.sku in
    <foreach collection="skuArr" item="el" separator="," open="(" close=")">
      #{el}
    </foreach>
  </select>
  <insert id="merge" parameterType="net.summerfarm.mall.model.vo.TrolleyVO" >
    INSERT INTO `trolley` (`m_id`, `account_id`,`sku`,`parent_sku`, suit_id, product_type,`quantity`, `update_time`, `biz_id`)
    VALUES (#{mId}, #{accountId} , #{sku},#{parentSku}, #{suitId}, #{productType}, #{quantity}, NOW(), #{bizId})
    ON DUPLICATE KEY UPDATE quantity = if(del_flag = 0 and #{sumFlag}, quantity, 0) + VALUES(quantity) ,update_time = NOW(), del_flag = 0
  </insert>

  <delete id="deleteByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.Trolley">
    delete from trolley
    where m_id = #{mId,jdbcType=BIGINT}
      and account_id = #{accountId, jdbcType=BIGINT}
      and suit_id = #{suitId}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.Trolley">
    insert into trolley (m_id, account_id, sku, quantity,
      `check`, update_time, del_flag
      )
    values (#{mId,jdbcType=BIGINT},#{accountId,jdbcType=BIGINT},#{sku,jdbcType=VARCHAR}, #{quantity,jdbcType=INTEGER},
      #{check,jdbcType=TINYINT}, #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.Trolley">
    insert into trolley
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        m_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="check != null">
        `check`,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT}
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="check != null">
        #{check,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=BIT},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=BIGINT}
      </if>
    </trim>
  </insert>

  <update id="cancelCheck" >
    UPDATE trolley
    SET `check` = 0
    where m_id = #{mId,jdbcType=BIGINT}
      and account_id = #{accountId,jdbcType=BIGINT}
      and sku = #{sku,jdbcType=VARCHAR}
      AND suit_id =#{suitId}
  </update>

  <update id="cancelChecked" >
    UPDATE trolley
    SET `check` = 0
    where m_id = #{mId,jdbcType=BIGINT}
      and account_id = #{accountId,jdbcType=BIGINT}
      and sku = #{sku,jdbcType=VARCHAR}
      and suit_id = 0
      and parent_sku = #{parentSku}
  </update>

  <update id="cancelCheckedSub" >
    UPDATE trolley
    SET `check` = 0
    where m_id = #{mId,jdbcType=BIGINT}
      and account_id = #{accountId,jdbcType=BIGINT}
      and suit_id = 0
      and parent_sku = #{parentSku}
  </update>

  <update id="updateQuantity" >
    UPDATE trolley
    SET quantity = #{quantity,jdbcType=INTEGER}
    where m_id = #{mId,jdbcType=BIGINT}
      and account_id = #{accountId,jdbcType=BIGINT}
      and sku = #{sku,jdbcType=VARCHAR}
      and product_type = #{productType}
  </update>

  <update id="updateQuantityByParentSku" >
    UPDATE trolley
    SET quantity = #{quantity,jdbcType=INTEGER}
    where m_id = #{mId,jdbcType=BIGINT}
      and account_id = #{accountId,jdbcType=BIGINT}
      and sku = #{sku,jdbcType=VARCHAR}
      and suit_id = 0
      and parent_sku = #{parentSku}
      and product_type = #{productType}
  </update>

  <update id="updateSelectiveBatch" parameterType="net.summerfarm.mall.model.domain.Trolley">
    update trolley
    SET
    <if test="quantity != null">
      quantity = #{quantity,jdbcType=INTEGER},
    </if>
    <if test="check != null">
      `check` = #{check,jdbcType=TINYINT},
    </if>
    <if test="delFlag != null">
      del_flag = #{delFlag,jdbcType=BIT},
    </if>
    update_time = now()
    where m_id = #{mId,jdbcType=BIGINT}
    and account_id = #{accountId,jdbcType=BIGINT}
  </update>


  <update id="updateToMain" parameterType="net.summerfarm.mall.model.domain.Trolley">
    update trolley
    SET
    parent_sku = 0,
    update_time = now()
    where m_id = #{mId,jdbcType=BIGINT}
        and account_id = #{accountId,jdbcType=BIGINT}
      <if test="sku != null">
        and sku = #{sku,jdbcType=VARCHAR}
      </if>
      and parent_sku = #{parentSku}
      AND suit_id = #{suitId}
      AND product_type = #{productType}
  </update>


  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.Trolley">
    update trolley
    SET
    <if test="quantity != null">
      quantity = #{quantity,jdbcType=INTEGER},
    </if>
    <if test="check != null">
      `check` = #{check,jdbcType=TINYINT},
    </if>
    <if test="delFlag != null">
      del_flag = #{delFlag,jdbcType=BIT},
    </if>
    <if test="productType != null">
     product_type = #{productType},
    </if>
    update_time = now()
    where m_id = #{mId,jdbcType=BIGINT}
    and account_id = #{accountId,jdbcType=BIGINT}
    <if test="sku != null">
      and sku = #{sku,jdbcType=VARCHAR}
    </if>
    AND suit_id = #{suitId}
    AND parent_sku = #{parentSku}
    AND product_type = #{productType}
  </update>

  <select id="selectRepeatSku" parameterType="net.summerfarm.mall.model.domain.Trolley"
          resultType="net.summerfarm.mall.model.domain.Trolley">
    SELECT m_id mId, account_id accountId, sku,parent_sku parentSku, suit_id suitId,product_type productType, quantity, `check`, del_flag delFlag
    FROM trolley
    WHERE m_id = #{mId}
    AND account_id = #{accountId}
    <if test="sku != null">
      AND sku = #{sku}
    </if>

    <if test="parentSku != null">
      AND `parent_sku` = #{parentSku}
    </if>

  </select>

  <select id="select" parameterType="net.summerfarm.mall.model.domain.Trolley"
          resultType="net.summerfarm.mall.model.domain.Trolley">
    SELECT m_id mId, account_id accountId, sku,parent_sku parentSku, suit_id suitId,product_type productType, quantity, `check`, del_flag delFlag
    FROM trolley
    WHERE m_id = #{mId}
    AND account_id = #{accountId}
    <if test="sku != null">
      AND sku = #{sku}
    </if>
    <if test="check != null">
      AND `check` = #{check}
    </if>
    <if test="parentSku != null">
      AND `parent_sku` = #{parentSku}
    </if>
    <if test="delFlag != null">
      AND del_flag = #{delFlag}
    </if>
    <if test="suitId != null">
      AND suit_id = #{suitId}
    </if>
    <if test="productType != null">
      AND product_type = #{productType}
    </if>
  </select>


  <select id="selectSuit" parameterType="net.summerfarm.mall.model.domain.Trolley"
          resultType="net.summerfarm.mall.model.domain.Trolley">
    SELECT m_id mId, account_id accountId, sku,parent_sku parentSku, suit_id suitId,product_type productType, quantity, `check`, del_flag delFlag
    FROM trolley
    WHERE m_id = #{mId}
    AND account_id = #{accountId}
    <if test="sku != null">
      AND sku = #{sku}
    </if>
    <if test="check != null">
      AND `check` = #{check}
    </if>
    <if test="parentSku != null">
      AND `parent_sku` = #{parentSku}
    </if>
    <if test="delFlag != null">
      AND del_flag = #{delFlag}
    </if>
    <if test="suitId != null">
      and suit_id = #{suitId}
    </if>
  </select>

  <select id="selectActivityAmount" resultType="java.math.BigDecimal">
    select sum(amount)  from (
        select t.sku , a.id ,a.name,(ask.sale_price - ask.activity_price) * t.quantity amount
        from trolley  t
        LEFT JOIN area_sku ak on t.sku = ak.sku
        inner join activity a on a.area_no = ak.area_no and a.type = 0 and a.status = 2 and a.start_time  <![CDATA[<=]]> now() and a.end_time >= now()
        inner join activity_sku ask on ask.sku = t.sku AND a.id = ask.activity_id AND ask.sku_status = 1
        where t.m_id = #{mId} and t.del_flag = 0 and t.product_type = 0 and t.`check` = 1 and ak.area_no = #{areaNo} and t.account_id = #{accountId}
    ) df
  </select>

  <select id="selectActivity" resultType="net.summerfarm.mall.model.input.OrderPreferentialOutput">
    select sum(amount) amount ,df.name  from (
        select t.sku , a.id ,a.name,(ask.sale_price - ask.activity_price) * t.quantity amount
        from trolley  t
        LEFT JOIN area_sku ak on t.sku = ak.sku
        inner join activity a on a.area_no = ak.area_no and a.type = 0 and a.status = 2 and a.start_time  <![CDATA[<=]]> now() and a.end_time >= now()
        inner join activity_sku ask on ask.sku = t.sku AND a.id = ask.activity_id AND ask.sku_status = 1
        where t.m_id = #{mId} and t.del_flag = 0 and t.product_type = 0 and t.`check` = 1 and ak.area_no = #{areaNo} and t.account_id = #{accountId}
    ) df group by df.id
  </select>
  <select id="selectActivityOrderNow" resultType="net.summerfarm.mall.model.input.OrderPreferentialOutput">
    select (ask.sale_price - ask.activity_price) amount, a.name,a.id
    from area_sku ak
    inner join activity a on a.area_no = ak.area_no and a.type = 0 and a.status = 2
        and a.start_time <![CDATA[<=]]> now() and a.end_time >= now()
    inner join activity_sku ask on ask.sku = ak.sku AND a.id = ask.activity_id AND ask.sku_status = 1
    where ak.area_no = #{areaNo} and ak.sku = #{sku}

  </select>
  <update id="clearGiftItem">
    update trolley set del_flag = 1 where product_type = 1 and m_id = #{mId} and account_id = #{accountId}
  </update>
  <select id="selectAreaNo" resultType="net.summerfarm.mall.model.domain.MarketRule">
    select show_name
    from market_rule
    where area_no = #{areaNo} and (name = '619乳制品测试' or name ='619非乳制品测试')
  </select>

  <select id="selectActivityPrice" resultType="java.lang.Integer">
    select IFNULL(sum((IFNULL(ak.price,i.sale_price) * sl.quantity)),0) AS sumPrice
    from market_rule_detail mrt left join category c on mrt.category_id = c.id
    left join products p on c.id= p.category_id
    left join inventory i on p.pd_id = i.pd_id
    left join trolley sl on i.sku = sl.sku
    left join area_sku ak on sl.sku = ak.sku
    where c.type=#{type} and sl.m_id=#{mId} and ak.area_no=#{areaNo} and sl.`check`=1 and mrt.sku = sl.sku and sl.del_flag = 0
  </select>
  <select id="selectSkuNum" resultType="net.summerfarm.mall.model.domain.Trolley">
    select sum(t.quantity) quantity
    from trolley t
    left join area_sku s on t.sku = s.sku
    where t.m_id = #{mId} and s.area_no = #{areaNo} and t.account_id = #{accountId} and t.sku=#{sku} and t.parent_sku = #{parentSku} and t.suit_id= #{suitId} and t.del_flag = 0 and t.product_type in(0,2)
  </select>

  <select id="selectChildCollocationItem" resultType="net.summerfarm.mall.model.vo.OrderItemVO">
    select t.sku , if(a.share = 0,a.quantity,ar.online_quantity) stock,a.on_sale onSale from  trolley t  LEFT JOIN area_sku a on t.sku = a.sku
                                  LEFT JOIN area area on a.area_no=area.area_no
                                  LEFT join (
      select area_no,store_no from fence where area_no = #{areaNo} and status = 0 limit 1
    ) f on f.area_no = a.area_no
                                  INNER JOIN warehouse_inventory_mapping wim on wim.store_no = f.store_no and wim.sku = t.sku
                                  INNER JOIN area_store ar on ar.sku=wim.sku and wim.warehouse_no = ar.area_no where t.m_id = #{mId}
                                                                                                                         AND t.account_id = #{accountId} and t.parent_sku = #{sku} and a.area_no =#{areaNo} and t.del_flag = 0;
  </select>
  <select id="selectCheckList" resultType="net.summerfarm.mall.model.domain.Trolley">
    select m_id mId,
           account_id accountId,
           sku,
           parent_sku parentSku,
           suit_id suitId,
           product_type productType,
           quantity,
           biz_id bizId
    from trolley where m_id = #{mId} and account_id = #{accountId} and del_flag = 0 and `check` = 1
  </select>

  <update id="cancelCollocationChildSkuBindParentSku">
    update trolley
    SET
      del_flag = 1,
      update_time = now()
    where m_id = #{mId,jdbcType=BIGINT}
    and account_id = #{accountId,jdbcType=BIGINT}
    <if test="sku != null">
      and sku = #{sku,jdbcType=VARCHAR}
    </if>
    and parent_sku = #{parentSku}
    AND product_type = #{productType}
    and del_flag = 0
  </update>

  <delete id="clearFailureSkuByTrolley" parameterType="net.summerfarm.mall.model.domain.Trolley">
    delete from trolley
    where m_id = #{mId,jdbcType=BIGINT}
      and account_id = #{accountId, jdbcType=BIGINT}
      and sku = #{sku}
  </delete>

  <delete id="clearSku" parameterType="net.summerfarm.mall.model.domain.Trolley">
    delete from trolley
    where m_id = #{mId,jdbcType=BIGINT}
      and account_id = #{accountId, jdbcType=BIGINT}
      and sku = #{sku} and product_type = #{productType}
  </delete>

  <!-- 查询客户购物车中所有sku-->
  <select id="listAllSku" resultType="string">
    select distinct sku
    from trolley
    where m_id = #{mId} and account_id = #{accountId}
      and del_flag = 0 and product_type = 0
  </select>
</mapper>