<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.OuterPlatformMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.OuterPlatform">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="outer_platform_id" jdbcType="INTEGER" property="outerPlatformId" />
    <result column="outer_platform_name" jdbcType="VARCHAR" property="outerPlatformName" />
    <result column="call_url" jdbcType="VARCHAR" property="callUrl" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="push_goods_switch" jdbcType="INTEGER" property="pushGoodsSwitch" />
    <result column="push_order_switch" jdbcType="INTEGER" property="pushOrderSwitch" />
    <result column="order_call_back_switch" jdbcType="INTEGER" property="orderCallBackSwitch" />
    <result column="push_store_switch" jdbcType="INTEGER" property="pushStoreSwitch" />
    <result column="order_report_switch" jdbcType="INTEGER" property="orderReportSwitch" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, outer_platform_id, outer_platform_name, call_url, token, push_goods_switch, push_order_switch, 
    order_call_back_switch, pushStoreSwitch, orderReportSwitch, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from outer_platform
    where id = #{id,jdbcType=BIGINT}
  </select>


  <select id="selectOuterPlatformById" resultType="net.summerfarm.mall.model.domain.OuterPlatform">
    select id,outer_platform_id outerPlatformId,outer_platform_name outerPlatformName,call_url callUrl,
           token,push_goods_switch pushGoodsSwitch,push_order_switch pushOrderSwitch,order_call_back_switch orderCallBackSwitch,
            push_store_switch pushStoreSwitch,order_report_switch orderReportSwitch
    from outer_platform
    where outer_platform_id = #{outerPlatformId}
  </select>

</mapper>